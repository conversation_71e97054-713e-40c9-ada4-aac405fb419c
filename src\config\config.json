{"api": {"omdb_api_key": "ecb12403", "omdb_base_url": "http://www.omdbapi.com/", "timeout": 10, "max_retries": 3}, "app": {"name": "Movie Search App", "version": "1.0.0", "debug": true, "host": "0.0.0.0", "port": 5000, "secret_key": "your-secret-key-change-this-in-production"}, "features": {"enable_popular_movies": true, "enable_api_endpoints": true, "enable_sharing": true, "enable_recommendations": true, "enable_preprocessing": true, "cache_duration": 3600}, "ui": {"movies_per_page": 12, "popular_movies_count": 10, "search_suggestions": ["Inception", "The Matrix", "Pulp Fiction", "The Godfather", "Titanic", "Avatar", "<PERSON>", "The Dark Knight"]}, "popular_movies": ["The Shawshank Redemption", "The Godfather", "The Dark Knight", "Pulp Fiction", "<PERSON>", "Inception", "The Matrix", "Goodfellas", "The Lord of the Rings: The Return of the King", "Fight Club"], "recommendations": {"data_file": "movies.csv", "processed_data_dir": "data/processed", "similarity_matrix_file": "similarity_matrix.pkl", "tfidf_matrix_file": "tfidf_matrix.pkl", "movie_indices_file": "movie_indices.pkl", "processed_movies_file": "processed_movies.pkl", "max_recommendations": 10, "min_similarity_score": 0.1, "text_features": ["genres", "keywords", "overview", "cast", "director"], "tfidf_params": {"max_features": 5000, "stop_words": "english", "ngram_range": [1, 2], "min_df": 2, "max_df": 0.8}, "preprocessing": {"remove_stopwords": true, "lemmatize": true, "lowercase": true, "remove_punctuation": true, "min_word_length": 2}}}