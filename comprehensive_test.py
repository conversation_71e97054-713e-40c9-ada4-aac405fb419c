#!/usr/bin/env python3
"""
Comprehensive test script for movie API functionality.
Includes tests for RapidAPI and alternative free APIs.
"""

import requests
import json
import time

def test_rapidapi_status():
    """Test if the RapidAPI key and host are working."""
    print("🔍 Testing RapidAPI Status...")
    
    RAPIDAPI_KEY = "**************************************************"
    RAPIDAPI_HOST = "imdb236.p.rapidapi.com"
    
    headers = {
        "X-RapidAPI-Key": RAPIDAPI_KEY,
        "X-RapidAPI-Host": RAPIDAPI_HOST
    }
    
    # Test basic connectivity
    try:
        response = requests.get(f"https://{RAPIDAPI_HOST}", headers=headers, timeout=10)
        print(f"✅ Host reachable: {RAPIDAPI_HOST}")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 401:
            print("❌ API Key issue - check if key is valid and subscription is active")
        elif response.status_code == 403:
            print("❌ Access forbidden - check API permissions")
        elif response.status_code == 404:
            print("❌ API not found - host might be incorrect")
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")

def test_alternative_free_apis():
    """Test free movie APIs as alternatives."""
    print("\n🎬 Testing Alternative Free Movie APIs...")
    
    # 1. OMDb API (requires free API key)
    print("\n1. Testing OMDb API (free with registration):")
    omdb_url = "http://www.omdbapi.com/?t=Inception&apikey=YOUR_KEY_HERE"
    print(f"   URL: {omdb_url}")
    print("   📝 Sign up at: http://www.omdbapi.com/apikey.aspx")
    
    # 2. The Movie Database (TMDb) - free
    print("\n2. Testing The Movie Database (TMDb):")
    try:
        # This is a public endpoint that doesn't require API key for basic search
        tmdb_url = "https://api.themoviedb.org/3/search/movie"
        params = {
            "api_key": "YOUR_API_KEY_HERE",  # You need to get this free key
            "query": "Inception"
        }
        print(f"   URL: {tmdb_url}")
        print("   📝 Get free API key at: https://www.themoviedb.org/settings/api")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # 3. Test a working public API (no key required)
    print("\n3. Testing JSONPlaceholder (demo API):")
    try:
        response = requests.get("https://jsonplaceholder.typicode.com/posts/1", timeout=10)
        if response.status_code == 200:
            print("   ✅ Internet connection and requests library working!")
            print(f"   📊 Sample response: {response.json()}")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

def create_mock_movie_function():
    """Create a mock function for testing your code structure."""
    print("\n🎭 Creating Mock Movie Function...")
    
    mock_code = '''
def get_movie_details_mock(title: str):
    """
    Mock function that returns sample movie data for testing.
    Use this while you set up the real API.
    """
    mock_data = {
        "Inception": {
            "plot": "A thief who steals corporate secrets through dream-sharing technology is given the inverse task of planting an idea into the mind of a C.E.O.",
            "poster": "https://m.media-amazon.com/images/M/MV5BMjAxMzY3NjcxNF5BMl5BanBnXkFtZTcwNTI5OTM0Mw@@._V1_SX300.jpg"
        },
        "The Matrix": {
            "plot": "A computer programmer is led to fight an underground war against powerful computers who have constructed his entire reality with a system called the Matrix.",
            "poster": "https://m.media-amazon.com/images/M/MV5BNzQzOTk3OTAtNDQ0Zi00ZTVkLWI0MTEtMDllZjNkYzNjNTc4L2ltYWdlXkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_SX300.jpg"
        }
    }
    
    if title in mock_data:
        return mock_data[title]["plot"], mock_data[title]["poster"]
    else:
        return "Mock plot for " + title, "https://via.placeholder.com/300x400?text=Movie+Poster"

# Test the mock function
print("Testing mock function:")
plot, poster = get_movie_details_mock("Inception")
print(f"Title: Inception")
print(f"Plot: {plot}")
print(f"Poster: {poster}")
'''
    
    print("📝 Mock function code:")
    print(mock_code)
    
    # Save mock function to a file
    with open("mock_movie_api.py", "w") as f:
        f.write(mock_code)
    print("💾 Saved mock function to 'mock_movie_api.py'")

def main():
    """Run all tests."""
    print("🚀 Starting Comprehensive Movie API Tests")
    print("=" * 60)
    
    test_rapidapi_status()
    test_alternative_free_apis()
    create_mock_movie_function()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY & RECOMMENDATIONS:")
    print("1. ✅ Your Python environment and requests library are working")
    print("2. ❌ The RapidAPI imdb236 endpoint appears to be unavailable")
    print("3. 💡 Consider these alternatives:")
    print("   - OMDb API (free): http://www.omdbapi.com/")
    print("   - TMDb API (free): https://www.themoviedb.org/")
    print("   - Use the mock function for development/testing")
    print("4. 🔧 Your code structure is good - just need to fix the API endpoint")

if __name__ == "__main__":
    main()
