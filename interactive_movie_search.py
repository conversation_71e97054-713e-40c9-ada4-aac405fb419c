#!/usr/bin/env python3
"""
Interactive Movie Search using OMDb API
Allows you to search for any movie interactively.
"""

import requests

def get_movie_details_omdb(title: str):
    """
    Retrieves movie plot and poster from the OMDb API.
    
    Args:
        title (str): The title of the movie to search for.
    
    Returns:
        tuple: A tuple containing (plot, poster_url) or ("N/A", "N/A") if not found or an error occurs.
    """
    OMDB_API_KEY = "ecb12403"
    OMDB_BASE_URL = "http://www.omdbapi.com/"

    # Parameters for the OMDb API
    params = {
        "t": title,  # 't' parameter searches by title
        "apikey": OMDB_API_KEY,
        "plot": "full"  # Get full plot instead of short
    }

    try:
        response = requests.get(OMDB_BASE_URL, params=params)
        response.raise_for_status()
        data = response.json()

        # Check if the movie was found
        if data.get("Response") == "True":
            plot = data.get("Plot", "N/A")
            poster = data.get("Poster", "N/A")
            
            # Additional movie info
            year = data.get("Year", "N/A")
            director = data.get("Director", "N/A")
            actors = data.get("Actors", "N/A")
            imdb_rating = data.get("imdbRating", "N/A")
            genre = data.get("Genre", "N/A")
            runtime = data.get("Runtime", "N/A")
            
            print(f"\n✅ Found movie: {data.get('Title', title)} ({year})")
            print(f"🎭 Genre: {genre}")
            print(f"⏱️ Runtime: {runtime}")
            print(f"📽️ Director: {director}")
            print(f"🎬 Actors: {actors}")
            print(f"⭐ IMDb Rating: {imdb_rating}")
            print(f"📖 Plot: {plot}")
            print(f"🖼️ Poster: {poster}")
            
            return plot, poster
        else:
            error_msg = data.get("Error", "Movie not found")
            print(f"\n❌ OMDb API Error: {error_msg}")
            return "N/A", "N/A"

    except requests.exceptions.RequestException as e:
        print(f"\n❌ Error fetching movie details: {e}")
        return "N/A", "N/A"

def main():
    """Interactive movie search."""
    print("🎬 Interactive Movie Search")
    print("=" * 40)
    print("Enter movie titles to search (or 'quit' to exit)")
    print("=" * 40)
    
    while True:
        try:
            movie_title = input("\n🔍 Enter movie title: ").strip()
            
            if movie_title.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not movie_title:
                print("❌ Please enter a movie title.")
                continue
            
            print(f"\n🔎 Searching for: '{movie_title}'...")
            plot, poster = get_movie_details_omdb(movie_title)
            
            print("\n" + "-" * 50)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
