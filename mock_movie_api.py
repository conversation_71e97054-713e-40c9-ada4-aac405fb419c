
def get_movie_details_mock(title: str):
    """
    Mock function that returns sample movie data for testing.
    Use this while you set up the real API.
    """
    mock_data = {
        "Inception": {
            "plot": "A thief who steals corporate secrets through dream-sharing technology is given the inverse task of planting an idea into the mind of a C.E.O.",
            "poster": "https://m.media-amazon.com/images/M/MV5BMjAxMzY3NjcxNF5BMl5BanBnXkFtZTcwNTI5OTM0Mw@@._V1_SX300.jpg"
        },
        "The Matrix": {
            "plot": "A computer programmer is led to fight an underground war against powerful computers who have constructed his entire reality with a system called the Matrix.",
            "poster": "https://m.media-amazon.com/images/M/MV5BNzQzOTk3OTAtNDQ0Zi00ZTVkLWI0MTEtMDllZjNkYzNjNTc4L2ltYWdlXkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_SX300.jpg"
        }
    }
    
    if title in mock_data:
        return mock_data[title]["plot"], mock_data[title]["poster"]
    else:
        return "Mock plot for " + title, "https://via.placeholder.com/300x400?text=Movie+Poster"

# Test the mock function
print("Testing mock function:")
plot, poster = get_movie_details_mock("Inception")
print(f"Title: Inception")
print(f"Plot: {plot}")
print(f"Poster: {poster}")
