import requests

def get_movie_details_omdb(title: str):
    """
    Retrieves movie plot and poster from the OMDb API.

    Args:
        title (str): The title of the movie to search for.

    Returns:
        tuple: A tuple containing (plot, poster_url) or ("N/A", "N/A") if not found or an error occurs.
    """
    OMDB_API_KEY = "ecb12403"
    OMDB_BASE_URL = "http://www.omdbapi.com/"

    # Parameters for the OMDb API
    params = {
        "t": title,  # 't' parameter searches by title
        "apikey": OMDB_API_KEY,
        "plot": "full"  # Get full plot instead of short
    }

    try:
        response = requests.get(OMDB_BASE_URL, params=params)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        data = response.json()

        # Check if the movie was found
        if data.get("Response") == "True":
            plot = data.get("Plot", "N/A")
            poster = data.get("Poster", "N/A")

            # Additional movie info available from OMDb
            year = data.get("Year", "N/A")
            director = data.get("Director", "N/A")
            actors = data.get("Actors", "N/A")
            imdb_rating = data.get("imdbRating", "N/A")

            print(f"✅ Found movie: {data.get('Title', title)} ({year})")
            print(f"📽️ Director: {director}")
            print(f"🎭 Actors: {actors}")
            print(f"⭐ IMDb Rating: {imdb_rating}")

            return plot, poster
        else:
            error_msg = data.get("Error", "Movie not found")
            print(f"❌ OMDb API Error: {error_msg}")
            return "N/A", "N/A"

    except requests.exceptions.RequestException as e:
        print(f"❌ Error fetching movie details from OMDb API: {e}")
        return "N/A", "N/A"


# Example usage:
if __name__ == "__main__":
    print("🎬 Movie Details Fetcher using OMDb API")
    print("=" * 50)

    # Test with Inception
    movie_title = "Inception"
    plot_summary, poster_url = get_movie_details_omdb(movie_title)

    print(f"\n🎥 Movie Title: {movie_title}")
    print(f"📖 Plot: {plot_summary}")
    print(f"🖼️ Poster URL: {poster_url}")

    print("\n" + "-" * 50)

    # Test with The Matrix
    movie_title_2 = "The Matrix"
    plot_summary_2, poster_url_2 = get_movie_details_omdb(movie_title_2)

    print(f"\n🎥 Movie Title: {movie_title_2}")
    print(f"📖 Plot: {plot_summary_2}")
    print(f"🖼️ Poster URL: {poster_url_2}")

    print("\n" + "-" * 50)

    # Test with a movie that doesn't exist
    movie_title_3 = "NonExistentMovie12345"
    plot_summary_3, poster_url_3 = get_movie_details_omdb(movie_title_3)

    print(f"\n🎥 Movie Title: {movie_title_3}")
    print(f"📖 Plot: {plot_summary_3}")
    print(f"🖼️ Poster URL: {poster_url_3}")