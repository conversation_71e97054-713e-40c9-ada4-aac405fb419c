#!/usr/bin/env python3
"""
Test script for the IMDb236 RapidAPI connection.
This script helps you verify the API key and find the correct endpoint.
"""

import requests
import json

def test_api_connection():
    """Test the basic API connection and explore available endpoints."""
    
    # Your API credentials
    RAPIDAPI_KEY = "**************************************************"
    RAPIDAPI_HOST = "imdb236.p.rapidapi.com"
    
    # Common endpoint patterns to try
    test_endpoints = [
        "/api/imdb/search-movie-by-title",
        "/search",
        "/movie/search",
        "/api/search",
        "/api/movie",
        "/",  # Root endpoint to see what's available
    ]
    
    headers = {
        "X-RapidAPI-Key": RAPIDAPI_KEY,
        "X-RapidAPI-Host": RAPIDAPI_HOST
    }
    
    print("Testing API connection...")
    print(f"Host: {RAPIDAPI_HOST}")
    print(f"API Key: {RAPIDAPI_KEY[:10]}...")
    print("-" * 50)
    
    for endpoint in test_endpoints:
        url = f"https://{RAPIDAPI_HOST}{endpoint}"
        print(f"\nTesting endpoint: {endpoint}")
        print(f"URL: {url}")
        
        try:
            # Try GET request first
            response = requests.get(url, headers=headers, timeout=10)
            print(f"GET Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ SUCCESS! This endpoint works.")
                try:
                    data = response.json()
                    print("Response preview:")
                    print(json.dumps(data, indent=2)[:500] + "..." if len(str(data)) > 500 else json.dumps(data, indent=2))
                except:
                    print("Response (text):", response.text[:200] + "..." if len(response.text) > 200 else response.text)
                break
            elif response.status_code == 404:
                print("❌ 404 - Endpoint not found")
            elif response.status_code == 401:
                print("❌ 401 - Unauthorized (check API key)")
            elif response.status_code == 403:
                print("❌ 403 - Forbidden (check permissions)")
            else:
                print(f"❌ {response.status_code} - {response.reason}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 50)
    print("NEXT STEPS:")
    print("1. Check the RapidAPI documentation for imdb236")
    print("2. Look for the correct endpoint path")
    print("3. Verify your API key is active")
    print("4. Check if you need to subscribe to the API")

def test_with_query():
    """Test with a movie query parameter."""
    RAPIDAPI_KEY = "**************************************************"
    RAPIDAPI_HOST = "imdb236.p.rapidapi.com"
    
    headers = {
        "X-RapidAPI-Key": RAPIDAPI_KEY,
        "X-RapidAPI-Host": RAPIDAPI_HOST
    }
    
    # Try different query parameter names
    query_params = [
        {"q": "Inception"},
        {"title": "Inception"},
        {"search": "Inception"},
        {"query": "Inception"},
    ]
    
    print("\n" + "=" * 50)
    print("Testing with query parameters...")
    
    for params in query_params:
        url = f"https://{RAPIDAPI_HOST}/"
        print(f"\nTrying params: {params}")
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✅ SUCCESS with query!")
                break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api_connection()
    test_with_query()
