{% extends "base.html" %}

{% block title %}Movie Search - Find Your Favorite Movies{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="text-center mb-4">
            <h1 class="text-white mb-3">
                <i class="fas fa-film me-3"></i>Discover Movies
            </h1>
            <p class="text-white-50 lead">Search for any movie and get detailed information instantly</p>
        </div>

        <div class="search-form">
            <form method="POST" action="{{ url_for('search_movie') }}">
                <div class="row g-3">
                    <div class="col-md-9">
                        <input type="text" 
                               class="form-control form-control-lg" 
                               name="movie_title" 
                               placeholder="Enter movie title (e.g., Inception, The Matrix, Titanic...)"
                               required
                               autocomplete="off">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </div>
            </form>
        </div>

        {% if error %}
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
        </div>
        {% endif %}

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>Try searching for:
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Inception</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>The Matrix</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Pulp Fiction</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>The Godfather</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Titanic</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Avatar</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Forrest Gump</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>The Dark Knight</li>
                        </ul>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('popular_movies') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-star me-2"></i>Popular Movies
                    </a>
                    <a href="{{ url_for('recommendations_page') }}" class="btn btn-outline-success">
                        <i class="fas fa-magic me-2"></i>Get Recommendations
                    </a>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-search fa-3x text-primary mb-3"></i>
                        <h5>Search Movies</h5>
                        <p class="text-muted">Find any movie by title and get comprehensive details</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-info-circle fa-3x text-success mb-3"></i>
                        <h5>Detailed Info</h5>
                        <p class="text-muted">Get plot, cast, ratings, and much more information</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-3x text-warning mb-3"></i>
                        <h5>IMDb Ratings</h5>
                        <p class="text-muted">See official IMDb ratings and user reviews</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendation Feature Highlight -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                    <div class="card-body text-center text-white">
                        <h4 class="mb-3">
                            <i class="fas fa-magic me-2"></i>AI-Powered Movie Recommendations
                        </h4>
                        <p class="lead mb-4">
                            Discover your next favorite movie with our intelligent recommendation system powered by machine learning
                        </p>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <i class="fas fa-brain fa-2x mb-2"></i>
                                    <h6>Smart Analysis</h6>
                                    <small>Analyzes plots, genres, cast, and directors</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <h6>Similarity Scoring</h6>
                                    <small>Shows percentage match for each recommendation</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <i class="fas fa-database fa-2x mb-2"></i>
                                    <h6>4,800+ Movies</h6>
                                    <small>Comprehensive database of films</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{{ url_for('recommendations_page') }}" class="btn btn-light btn-lg me-2">
                                <i class="fas fa-magic me-2"></i>Try Recommendations
                            </a>
                            <button class="btn btn-outline-light btn-lg quick-recommend-demo" data-movie="Inception">
                                <i class="fas fa-play me-2"></i>Demo: Movies like Inception
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Add some interactivity
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('input[name="movie_title"]');
        const searchForm = document.querySelector('form');
        
        // Focus on search input when page loads
        searchInput.focus();
        
        // Add loading state when form is submitted
        searchForm.addEventListener('submit', function() {
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
            submitBtn.disabled = true;
        });

        // Quick recommendation demo button
        const demoBtn = document.querySelector('.quick-recommend-demo');
        if (demoBtn) {
            demoBtn.addEventListener('click', function() {
                const movieTitle = this.getAttribute('data-movie');
                window.location.href = `/recommendations/${encodeURIComponent(movieTitle)}`;
            });
        }
    });
</script>
{% endblock %}
